<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scroll Cards Animation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            overflow-x: hidden;
        }

        .container {
            height: 500vh; /* 5x viewport height for scrolling */
            position: relative;
        }

        .fixed-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: radial-gradient(circle at center, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: 1;
        }

        .scroll-content {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .cards-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card {
            position: absolute;
            width: 300px;
            height: 200px;
            background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .card h3 {
            color: #ffffff;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .card p {
            color: rgba(255,255,255,0.7);
            font-size: 16px;
            line-height: 1.5;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
        }

        /* Initial positions - cards start from edges */
        .card:nth-child(1) {
            transform: translate(-800px, -400px) rotate(-15deg) scale(0.8);
        }
        .card:nth-child(2) {
            transform: translate(800px, -300px) rotate(20deg) scale(0.8);
        }
        .card:nth-child(3) {
            transform: translate(-700px, 200px) rotate(10deg) scale(0.8);
        }
        .card:nth-child(4) {
            transform: translate(900px, 300px) rotate(-25deg) scale(0.8);
        }
        .card:nth-child(5) {
            transform: translate(0px, -600px) rotate(5deg) scale(0.8);
        }
        .card:nth-child(6) {
            transform: translate(-600px, 0px) rotate(-10deg) scale(0.8);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            z-index: 100;
            transition: width 0.1s ease;
        }

        .scroll-indicator {
            position: fixed;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255,255,255,0.6);
            font-size: 14px;
            z-index: 50;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        .title {
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            color: #ffffff;
            font-size: 48px;
            font-weight: 700;
            text-align: center;
            z-index: 50;
            opacity: 0;
            transition: opacity 0.6s ease;
        }

        @media (max-width: 768px) {
            .card {
                width: 250px;
                height: 180px;
                padding: 20px;
            }
            
            .card h3 {
                font-size: 20px;
            }
            
            .card p {
                font-size: 14px;
            }
            
            .title {
                font-size: 32px;
                top: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    <div class="fixed-background"></div>
    
    <div class="container">
        <div class="scroll-content">
            <div class="title" id="title">Scroll to Explore</div>
            <div class="scroll-indicator" id="scrollIndicator">↓ Scroll Down ↓</div>
            
            <div class="cards-container">
                <div class="card">
                    <div class="card-icon">🚀</div>
                    <h3>Innovation</h3>
                    <p>Pushing boundaries and exploring new possibilities in technology</p>
                </div>
                <div class="card">
                    <div class="card-icon">💡</div>
                    <h3>Creativity</h3>
                    <p>Transforming ideas into beautiful and functional experiences</p>
                </div>
                <div class="card">
                    <div class="card-icon">⭐</div>
                    <h3>Excellence</h3>
                    <p>Delivering quality solutions that exceed expectations</p>
                </div>
                <div class="card">
                    <div class="card-icon">🎯</div>
                    <h3>Focus</h3>
                    <p>Maintaining clarity of vision while executing precise strategies</p>
                </div>
                <div class="card">
                    <div class="card-icon">🔥</div>
                    <h3>Passion</h3>
                    <p>Bringing energy and enthusiasm to every project we undertake</p>
                </div>
                <div class="card">
                    <div class="card-icon">🌟</div>
                    <h3>Impact</h3>
                    <p>Creating meaningful change that resonates with users worldwide</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let scrollProgress = 0;
        const cards = document.querySelectorAll('.card');
        const progressBar = document.getElementById('progressBar');
        const title = document.getElementById('title');
        const scrollIndicator = document.getElementById('scrollIndicator');

        // Initial positions for each card
        const initialPositions = [
            { x: -800, y: -400, rotation: -15 },
            { x: 800, y: -300, rotation: 20 },
            { x: -700, y: 200, rotation: 10 },
            { x: 900, y: 300, rotation: -25 },
            { x: 0, y: -600, rotation: 5 },
            { x: -600, y: 0, rotation: -10 }
        ];

        // Final position (center for all cards)
        const finalPosition = { x: 0, y: 0, rotation: 0 };

        function updateAnimation() {
            const scrollTop = window.pageYOffset;
            const maxScroll = document.body.scrollHeight - window.innerHeight;
            scrollProgress = Math.min(scrollTop / maxScroll, 1);

            // Update progress bar
            progressBar.style.width = `${scrollProgress * 100}%`;

            // Show/hide title and scroll indicator
            if (scrollProgress > 0.1) {
                title.style.opacity = '1';
                scrollIndicator.style.opacity = '0';
            } else {
                title.style.opacity = '0';
                scrollIndicator.style.opacity = '1';
            }

            // Sequential card movement
            const N = cards.length;
            cards.forEach((card, index) => {
                const initial = initialPositions[index];
                const final = finalPosition;
                // Each card moves in its own segment
                const segStart = index / N;
                const segEnd = (index + 1) / N;
                let localProgress = 0;
                if (scrollProgress <= segStart) {
                    localProgress = 0;
                } else if (scrollProgress >= segEnd) {
                    localProgress = 1;
                } else {
                    localProgress = (scrollProgress - segStart) / (segEnd - segStart);
                }
                const easeProgress = easeInOutCubic(localProgress);
                const currentX = initial.x + (final.x - initial.x) * easeProgress;
                const currentY = initial.y + (final.y - initial.y) * easeProgress;
                const currentRotation = initial.rotation + (final.rotation - initial.rotation) * easeProgress;
                const currentScale = 0.8 + (1 - 0.8) * easeProgress;
                card.style.transform = `translate(${currentX}px, ${currentY}px) rotate(${currentRotation}deg) scale(${currentScale})`;
                card.style.opacity = 0.3 + (0.7 * easeProgress);
            });
        }

        function easeInOutCubic(t) {
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }

        // Throttled scroll event
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateAnimation();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', onScroll);
        
        // Initialize animation
        updateAnimation();

        // Smooth scrolling for better experience
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
</body>
</html>